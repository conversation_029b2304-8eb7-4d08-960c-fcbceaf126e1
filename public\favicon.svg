<svg width="32" height="32" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="gradientFill" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#3B82F6" />
      <stop offset="100%" stop-color="#4F46E5" />
    </linearGradient>
  </defs>
  <circle cx="16" cy="16" r="16" fill="url(#gradientFill)" />
  <path d="M22.3 10.2C22.3 9.5 21.7 9 21 9H9.3C8.5 9 8 9.5 8 10.2V18.8C8 19.5 8.5 20 9.3 20H11L12.9 22L14.8 20H21C21.7 20 22.3 19.5 22.3 18.8V10.2Z" fill="white" fill-opacity="0.9"/>
  <path d="M24 14.2C24 13.5 23.5 13 22.7 13H22V18.8C22 20 21 21 19.7 21H14.8L12.9 23L11 21H10V22C10 22.8 10.5 23.3 11.3 23.3H14L15.9 25.3L17.8 23.3H22.7C23.5 23.3 24 22.8 24 22V14.2Z" fill="white" fill-opacity="0.7"/>
  <circle cx="11" cy="14.5" r="1" fill="url(#gradientFill)"/>
  <circle cx="14.5" cy="14.5" r="1" fill="url(#gradientFill)"/>
  <circle cx="18" cy="14.5" r="1" fill="url(#gradientFill)"/>
</svg> 