'use client';

import React, { Suspense, lazy } from 'react';
import { Loader2 } from 'lucide-react';

// Lazy load heavy chat components
const MessageRenderer = lazy(() => import('./MessageRenderer'));
const CodeBlock = lazy(() => import('./CodeBlock'));
const FormattedMessage = lazy(() => import('./FormattedMessage'));

// Loading fallbacks
const MessageRendererFallback = () => (
  <div className="flex items-center gap-2 p-4 rounded-lg bg-muted/50">
    <Loader2 className="h-4 w-4 animate-spin" />
    <span className="text-sm text-muted-foreground">Loading message...</span>
  </div>
);

const CodeBlockFallback = ({ language, value }: { language?: string; value?: string }) => (
  <div className="relative group rounded-md overflow-hidden my-4 bg-slate-800 dark:bg-slate-900">
    <div className="bg-slate-800 dark:bg-slate-900 flex items-center justify-between px-4 py-1.5 text-xs text-slate-300 dark:text-slate-400">
      <div className="flex items-center gap-2">
        <span className="uppercase">{language || 'Code'}</span>
      </div>
      <Loader2 className="h-4 w-4 animate-spin" />
    </div>
    <pre className="bg-slate-800 dark:bg-slate-900 p-4 rounded-b-md overflow-x-auto">
      <code className="text-slate-200 dark:text-slate-300 text-sm">{value || 'Loading...'}</code>
    </pre>
  </div>
);

const FormattedMessageFallback = ({ content }: { content?: string }) => (
  <div className="flex items-center gap-2 p-2">
    <Loader2 className="h-4 w-4 animate-spin" />
    <span className="text-sm">{content || 'Loading...'}</span>
  </div>
);

// Lazy component wrappers
export const LazyMessageRenderer = (props: any) => (
  <Suspense fallback={<MessageRendererFallback />}>
    <MessageRenderer {...props} />
  </Suspense>
);

export const LazyCodeBlock = (props: any) => (
  <Suspense fallback={<CodeBlockFallback language={props.language} value={props.value} />}>
    <CodeBlock {...props} />
  </Suspense>
);

export const LazyFormattedMessage = (props: any) => (
  <Suspense fallback={<FormattedMessageFallback content={props.content} />}>
    <FormattedMessage {...props} />
  </Suspense>
);

// Export individual components for convenience
export { MessageRenderer, CodeBlock, FormattedMessage };
