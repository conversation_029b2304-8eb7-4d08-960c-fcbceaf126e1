'use client';

import React, { Suspense, lazy } from 'react';
import { Loader2 } from 'lucide-react';
import { useTheme } from 'next-themes';

// Lazy load react-markdown to reduce initial bundle size
const ReactMarkdown = lazy(() => import('react-markdown'));

// Lazy load the dynamic syntax highlighter
const DynamicSyntaxHighlighter = lazy(() => import('./DynamicSyntaxHighlighter'));

interface DynamicMarkdownProps {
  children: string;
  className?: string;
  components?: any;
}

const MarkdownFallback = ({ children }: { children: string }) => (
  <div className="prose prose-slate dark:prose-invert max-w-none break-words">
    <div className="flex items-center gap-2 mb-4">
      <Loader2 className="h-4 w-4 animate-spin" />
      <span className="text-sm text-muted-foreground">Loading content...</span>
    </div>
    <div className="whitespace-pre-wrap text-sm">{children}</div>
  </div>
);

export default function DynamicMarkdown({
  children,
  className = "prose prose-slate dark:prose-invert max-w-none break-words",
  components,
  ...props
}: DynamicMarkdownProps) {
  const { theme } = useTheme();

  const defaultComponents = {
    code({ node, inline, className, children, ...props }: any) {
      const match = /language-(\w+)/.exec(className || '');
      const language = match ? match[1] : '';
      
      return !inline && match ? (
        <Suspense fallback={
          <div className="bg-slate-800 rounded p-4 my-2">
            <code className="text-slate-300">{children}</code>
          </div>
        }>
          <DynamicSyntaxHighlighter
            language={language}
            theme={theme === 'dark' ? 'dark' : 'light'}
            {...props}
          >
            {String(children).replace(/\n$/, '')}
          </DynamicSyntaxHighlighter>
        </Suspense>
      ) : (
        <code className={`${className || ''} p-1 rounded bg-gray-200 dark:bg-gray-800`} {...props}>
          {children}
        </code>
      );
    },
    ...components,
  };

  return (
    <Suspense fallback={<MarkdownFallback children={children} />}>
      <div className={className}>
        <ReactMarkdown
          components={defaultComponents}
          {...props}
        >
          {children}
        </ReactMarkdown>
      </div>
    </Suspense>
  );
}
