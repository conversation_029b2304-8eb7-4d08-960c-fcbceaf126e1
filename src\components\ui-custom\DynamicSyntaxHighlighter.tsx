'use client';

import React, { Suspense, lazy } from 'react';
import { Loader2 } from 'lucide-react';

// <PERSON><PERSON> load the syntax highlighter to reduce initial bundle size
const SyntaxHighlighter = lazy(() =>
  import('react-syntax-highlighter').then(module => ({
    default: module.Prism
  }))
);

// Lazy load styles based on theme preference
const loadDarkStyle = () =>
  import('react-syntax-highlighter/dist/esm/styles/prism/vsc-dark-plus').then(module =>
    module.default
  );

const loadLightStyle = () =>
  import('react-syntax-highlighter/dist/esm/styles/prism/vs').then(module =>
    module.default
  );

// Common languages that are loaded on demand
const loadLanguage = async (language: string) => {
  try {
    switch (language.toLowerCase()) {
      case 'javascript':
      case 'js':
        return import('react-syntax-highlighter/dist/esm/languages/prism/javascript');
      case 'typescript':
      case 'ts':
        return import('react-syntax-highlighter/dist/esm/languages/prism/typescript');
      case 'python':
      case 'py':
        return import('react-syntax-highlighter/dist/esm/languages/prism/python');
      case 'java':
        return import('react-syntax-highlighter/dist/esm/languages/prism/java');
      case 'css':
        return import('react-syntax-highlighter/dist/esm/languages/prism/css');
      case 'html':
        return import('react-syntax-highlighter/dist/esm/languages/prism/markup');
      case 'json':
        return import('react-syntax-highlighter/dist/esm/languages/prism/json');
      case 'bash':
      case 'shell':
        return import('react-syntax-highlighter/dist/esm/languages/prism/bash');
      case 'sql':
        return import('react-syntax-highlighter/dist/esm/languages/prism/sql');
      case 'jsx':
        return import('react-syntax-highlighter/dist/esm/languages/prism/jsx');
      case 'tsx':
        return import('react-syntax-highlighter/dist/esm/languages/prism/tsx');
      default:
        return null;
    }
  } catch (error) {
    console.warn(`Failed to load language: ${language}`, error);
    return null;
  }
};

interface DynamicSyntaxHighlighterProps {
  language: string;
  children: string;
  customStyle?: React.CSSProperties;
  showLineNumbers?: boolean;
  theme?: 'light' | 'dark';
  [key: string]: any;
}

const SyntaxHighlighterFallback = ({ children, language }: { children: string; language: string }) => (
  <div className="relative bg-slate-800 dark:bg-slate-900 rounded-md p-4 my-4">
    <div className="flex items-center justify-between mb-2">
      <span className="text-xs text-slate-400 uppercase">{language || 'Code'}</span>
      <Loader2 className="h-4 w-4 text-slate-400 animate-spin" />
    </div>
    <pre className="text-sm text-slate-300 overflow-x-auto">
      <code>{children}</code>
    </pre>
  </div>
);

export default function DynamicSyntaxHighlighter({
  language,
  children,
  customStyle,
  showLineNumbers = false,
  theme = 'dark',
  ...props
}: DynamicSyntaxHighlighterProps) {
  const [style, setStyle] = React.useState(null);
  const [languageLoaded, setLanguageLoaded] = React.useState(false);

  React.useEffect(() => {
    // Load the appropriate style based on theme
    const styleLoader = theme === 'dark' ? loadDarkStyle : loadLightStyle;
    styleLoader().then(setStyle);
  }, [theme]);

  React.useEffect(() => {
    // Load the specific language if available
    loadLanguage(language).then(() => {
      setLanguageLoaded(true);
    });
  }, [language]);

  return (
    <Suspense fallback={<SyntaxHighlighterFallback children={children} language={language} />}>
      <SyntaxHighlighter
        language={language}
        style={style}
        showLineNumbers={showLineNumbers}
        customStyle={{
          borderRadius: '0.5rem',
          padding: '1rem',
          marginTop: '0.5rem',
          marginBottom: '0.5rem',
          ...customStyle,
        }}
        {...props}
      >
        {children}
      </SyntaxHighlighter>
    </Suspense>
  );
}
