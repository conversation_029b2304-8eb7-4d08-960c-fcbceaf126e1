const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Load environment variables
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase credentials. Please check your .env.local file.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function fixChatRpcError() {
  console.log('🔧 Fixing chat RPC error...');

  try {
    console.log('📄 Since we cannot execute DDL commands via the Supabase client,');
    console.log('   you will need to run the SQL commands manually in Supabase SQL Editor.');
    console.log('');

    // Check current chats table structure
    console.log('🔍 Checking current chats table structure...');

    const { data: testChat, error: testError } = await supabase
      .from('chats')
      .select('*')
      .limit(1);

    if (testError) {
      console.error('❌ Error accessing chats table:', testError.message);
      return false;
    }

    console.log('✅ Chats table is accessible');

    // Test if we can insert with user_email and user_name
    console.log('🧪 Testing if user_email and user_name columns exist...');

    // First, let's check the actual table structure
    const { data: tableInfo, error: tableError } = await supabase
      .from('information_schema.columns')
      .select('column_name')
      .eq('table_name', 'chats')
      .eq('table_schema', 'public');

    if (tableError) {
      console.log('⚠️  Could not check table structure directly, trying insert test...');
    } else {
      const columnNames = tableInfo?.map(col => col.column_name) || [];
      console.log('📋 Current chats table columns:', columnNames.join(', '));

      if (columnNames.includes('user_email') && columnNames.includes('user_name')) {
        console.log('✅ Both user_email and user_name columns exist!');
      } else {
        console.log('❌ Missing columns:',
          !columnNames.includes('user_email') ? 'user_email' : '',
          !columnNames.includes('user_name') ? 'user_name' : ''
        );
      }
    }

    // Now test with a dummy insert (this will fail due to foreign key, but that's expected)
    const testUserId = '00000000-0000-0000-0000-000000000001';
    const { data: insertTest, error: insertError } = await supabase
      .from('chats')
      .insert({
        user_id: testUserId,
        title: 'Test Chat for Column Check',
        model: 'test',
        user_email: '<EMAIL>',
        user_name: 'Test User'
      })
      .select('id')
      .single();

    if (insertError) {
      if (insertError.message.includes('foreign key constraint')) {
        console.log('✅ Foreign key constraint error is expected (test user doesn\'t exist)');
        console.log('✅ This means the columns exist and the table structure is correct!');
      } else if (insertError.message.includes('column') || insertError.message.includes('user_email') || insertError.message.includes('user_name')) {
        console.log('❌ Missing columns detected. You need to run the SQL migration.');
        console.log('');
        console.log('📋 Please copy and paste the following SQL into your Supabase SQL Editor:');
        console.log('');
        console.log('-- Add missing columns to chats table');
        console.log('ALTER TABLE chats ADD COLUMN IF NOT EXISTS user_email TEXT;');
        console.log('ALTER TABLE chats ADD COLUMN IF NOT EXISTS user_name TEXT;');
        console.log('');
        console.log('-- Create the RPC function');
        console.log(`CREATE OR REPLACE FUNCTION save_chat_with_user_info(
  p_user_id UUID,
  p_title TEXT,
  p_model TEXT,
  p_user_email TEXT,
  p_user_name TEXT
)
RETURNS UUID AS $$
DECLARE
  v_chat_id UUID;
BEGIN
  INSERT INTO chats (
    user_id,
    title,
    model,
    user_email,
    user_name,
    created_at,
    updated_at
  ) VALUES (
    p_user_id,
    p_title,
    p_model,
    p_user_email,
    p_user_name,
    NOW(),
    NOW()
  )
  RETURNING id INTO v_chat_id;

  RETURN v_chat_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;`);
        console.log('');
        return false;
      } else {
        console.error('❌ Unexpected error during column test:', insertError.message);
        return false;
      }
    } else {
      console.log('✅ Columns exist and insert test passed!');
    }

    // Clean up test data
    if (insertTest?.id) {
      await supabase
        .from('chats')
        .delete()
        .eq('id', insertTest.id);
    }

    console.log('✅ Columns already exist!');

    // Test the RPC function
    console.log('🧪 Testing the save_chat_with_user_info RPC function...');

    const rpcTestUserId = '00000000-0000-0000-0000-000000000001';
    const { data: rpcTestData, error: rpcTestError } = await supabase.rpc('save_chat_with_user_info', {
      p_user_id: rpcTestUserId,
      p_title: 'Test RPC Chat',
      p_model: 'gpt-4',
      p_user_email: '<EMAIL>',
      p_user_name: 'Test User'
    });

    if (rpcTestError) {
      if (rpcTestError.message.includes('function') && rpcTestError.message.includes('does not exist')) {
        console.log('❌ RPC function does not exist. Please run the SQL above to create it.');
        return false;
      } else if (rpcTestError.message.includes('foreign key constraint')) {
        console.log('✅ RPC function exists and works! (Foreign key error is expected with test UUID)');
      } else {
        console.error('❌ RPC function test failed:', rpcTestError.message);
        return false;
      }
    } else {
      console.log('✅ RPC function test successful, chat ID:', rpcTestData);

      // Clean up test data if it was actually created
      if (rpcTestData) {
        await supabase
          .from('chats')
          .delete()
          .eq('id', rpcTestData);
        console.log('🧹 Cleaned up test data');
      }
    }



    return true;
  } catch (error) {
    console.error('❌ Unexpected error:', error.message);
    return false;
  }
}

// Run the fix
fixChatRpcError().then(success => {
  if (success) {
    console.log('🎉 Chat RPC error fix verification completed successfully!');
    console.log('');
    console.log('✅ All database components are working correctly:');
    console.log('1. ✅ user_email and user_name columns exist in chats table');
    console.log('2. ✅ save_chat_with_user_info RPC function is working');
    console.log('3. ✅ Improved error handling in userService.ts');
    console.log('');
    console.log('🎯 You can now create chats without encountering the RPC error.');
  } else {
    console.log('❌ Database migration needed. Please run the SQL commands shown above in Supabase SQL Editor.');
    console.log('');
    console.log('📖 Instructions:');
    console.log('1. Go to your Supabase dashboard');
    console.log('2. Navigate to SQL Editor');
    console.log('3. Copy and paste the SQL commands shown above');
    console.log('4. Run the SQL commands');
    console.log('5. Run this script again to verify the fix');
    process.exit(1);
  }
});
