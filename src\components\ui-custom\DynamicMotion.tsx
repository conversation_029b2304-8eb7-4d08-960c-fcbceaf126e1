'use client';

import React, { Suspense, lazy } from 'react';

// Lazy load framer-motion components to reduce initial bundle size
const MotionDiv = lazy(() => 
  import('framer-motion').then(module => ({
    default: module.motion.div
  }))
);

const MotionButton = lazy(() => 
  import('framer-motion').then(module => ({
    default: module.motion.button
  }))
);

const MotionSpan = lazy(() =>
  import('framer-motion').then(module => ({
    default: module.motion.span
  }))
);

const MotionP = lazy(() =>
  import('framer-motion').then(module => ({
    default: module.motion.p
  }))
);

const MotionH2 = lazy(() =>
  import('framer-motion').then(module => ({
    default: module.motion.h2
  }))
);

const AnimatePresence = lazy(() => 
  import('framer-motion').then(module => ({
    default: module.AnimatePresence
  }))
);

interface DynamicMotionProps {
  children: React.ReactNode;
  as?: 'div' | 'button' | 'span' | 'p' | 'h2';
  fallback?: React.ReactNode;
  [key: string]: any;
}

// Fallback component that renders without animation
const StaticFallback = ({ 
  children, 
  as = 'div', 
  className, 
  style,
  onClick,
  ...props 
}: DynamicMotionProps) => {
  const Component = as;
  return (
    <Component 
      className={className} 
      style={style}
      onClick={onClick}
      {...props}
    >
      {children}
    </Component>
  );
};

export function DynamicMotion({
  children,
  as = 'div',
  fallback,
  ...props
}: DynamicMotionProps) {
  const MotionComponent = as === 'button' ? MotionButton :
                         as === 'span' ? MotionSpan :
                         as === 'p' ? MotionP :
                         as === 'h2' ? MotionH2 :
                         MotionDiv;

  return (
    <Suspense fallback={fallback || <StaticFallback as={as} {...props}>{children}</StaticFallback>}>
      <MotionComponent {...props}>
        {children}
      </MotionComponent>
    </Suspense>
  );
}

export function DynamicAnimatePresence({ 
  children, 
  fallback,
  ...props 
}: { 
  children: React.ReactNode; 
  fallback?: React.ReactNode;
  [key: string]: any;
}) {
  return (
    <Suspense fallback={fallback || <div>{children}</div>}>
      <AnimatePresence {...props}>
        {children}
      </AnimatePresence>
    </Suspense>
  );
}

// Export individual motion components for convenience
export const DynamicMotionDiv = (props: any) => <DynamicMotion as="div" {...props} />;
export const DynamicMotionButton = (props: any) => <DynamicMotion as="button" {...props} />;
export const DynamicMotionSpan = (props: any) => <DynamicMotion as="span" {...props} />;
export const DynamicMotionP = (props: any) => <DynamicMotion as="p" {...props} />;
export const DynamicMotionH2 = (props: any) => <DynamicMotion as="h2" {...props} />;

export default DynamicMotion;
