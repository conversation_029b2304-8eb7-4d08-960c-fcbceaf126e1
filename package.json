{"name": "chatbuddy", "version": "2.6", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "analyze": "cross-env ANALYZE=true next build", "setup": "node setup.js", "setup-supabase": "node setup-supabase.js", "generate-favicon": "node scripts/create-favicon.js", "cleanup-db": "node scripts/cleanup-duplicates.mjs", "migrate-api-keys": "node scripts/migrate-api-keys.js", "check-database": "node scripts/check-database.js", "fix-ai-providers": "node scripts/add-ai-providers-column.js", "fix-profile-columns": "node scripts/fix-profile-columns.js", "fix-database": "node scripts/fix-database-with-supabase.js", "cleanup-profiles": "node scripts/cleanup-duplicate-profiles.js", "check-ui-errors": "node scripts/check-ui-errors.js", "check-auth": "node scripts/check-auth-status.js", "fix-chat-rpc": "node scripts/fix-chat-rpc-error.js", "verify-chat-persistence": "node scripts/verify-chat-persistence.js", "verify-supabase": "node scripts/simple-verify.js"}, "dependencies": {"@radix-ui/react-accordion": "1.2.8", "@radix-ui/react-alert-dialog": "1.1.14", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "1.2.3", "@radix-ui/react-collapsible": "1.1.3", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "1.1.4", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "1.1.9", "@radix-ui/react-toast": "^1.2.6", "@radix-ui/react-toggle": "^1.1.2", "@radix-ui/react-tooltip": "^1.1.8", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/auth-helpers-react": "0.5.0", "@supabase/supabase-js": "2.52.1", "@types/uuid": "^10.0.0", "buffer": "6.0.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "copy-to-clipboard": "^3.3.3", "critters": "^0.0.23", "crypto-browserify": "^3.12.1", "date-fns": "^4.1.0", "dotenv": "16.6.1", "framer-motion": "^12.6.3", "geist": "^1.3.1", "lucide-react": "^0.487.0", "next": "15.4.3", "next-themes": "^0.4.6", "node-fetch": "3.3.2", "node-libs-browser": "^2.2.1", "path-browserify": "^1.0.1", "prismjs": "^1.30.0", "react": "^19.0.0", "react-colorful": "^5.6.1", "react-dom": "^19.0.0", "react-error-boundary": "5.0.0", "react-markdown": "^10.1.0", "react-speech-recognition": "^4.0.0", "react-syntax-highlighter": "^15.6.1", "remark-gfm": "^4.0.1", "stream-browserify": "^3.0.0", "tailwind-merge": "^3.1.0", "tailwindcss-animate": "^1.0.7", "url": "^0.11.4", "uuid": "^11.1.0", "web-speech-cognitive-services": "^8.1.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@netlify/plugin-nextjs": "5.10.3", "@next/bundle-analyzer": "15.4.4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/react-syntax-highlighter": "^15.5.13", "autoprefixer": "^10.4.21", "canvas": "3.1.0", "cross-env": "10.0.0", "eslint": "^9", "eslint-config-next": "15.4.3", "postcss": "^8.5.3", "sharp": "0.34.1", "tailwindcss": "^3.4.0", "typescript": "^5", "webpack": "5.98.0"}}